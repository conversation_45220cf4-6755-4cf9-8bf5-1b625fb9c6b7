"use client"
import { useState } from 'react';
import SearchTabs from '../ui/SearchTabs';
import SearchInput from '../ui/SearchInput';
import PromotionalBanner from '../ui/PromotionalBanner';

const HeroSection = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 pt-14 sm:pt-16">
      {/* Main Hero Content */}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-8 sm:py-12 lg:py-16">
        {/* Hero Title */}
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
            Where to?
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Discover amazing places, experiences, and create memories that last a lifetime
          </p>
        </div>

        {/* Search Interface */}
        <div className="max-w-4xl mx-auto mb-12 sm:mb-16">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
            <SearchTabs activeTab={activeTab} onTabChange={setActiveTab} />
            <SearchInput searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          </div>
        </div>

        {/* Promotional Banner */}
        <PromotionalBanner />
      </div>
    </section>
  );
};

export default HeroSection;